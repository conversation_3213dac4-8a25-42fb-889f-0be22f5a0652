import {
  ArrowDown,
  Bell,
  Blocks,
  Boxes,
  Container,
  CreditCard,
  DatabaseBackup,
  FastForward,
  FileLock2,
  Files,
  FileSearch2,
  FileText,
  Handshake,
  House,
  Info,
  LayoutDashboard,
  LayoutGrid,
  MailQuestion,
  MapPinned,
  Package,
  Package2,
  PackagePlus,
  Receipt,
  ReceiptIndianRupee,
  Scale,
  Scan,
  Ship,
  Truck,
  TruckElectric,
  UploadIcon,
  User,
  Users,
  Warehouse,
} from "lucide-react";

export const navLinks = [
  { label: "Home", href: "/customer/home", icon: House, access: "Customer" },
  {
    label: "Dashboard",
    href: "/customer/dashboard",
    icon: LayoutDashboard,
    access: "Customer",
  },
  {
    label: "CFS",
    href: "",
    icon: Ship,
    access: "Customer",
    subItems: [
      {
        label: "Orders",
        href: "/customer/cfs/orders",
        access: "Customer",
        icon: Package,
      },
      {
        label: "Pricing Requests",
        href: "/customer/cfs/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "Customer",
      },
      {
        label: "Service Requests",
        href: "/customer/cfs/requests",
        icon: MailQuestion,
        access: "Customer",
      },
      {
        label: "Services",
        href: "/customer/cfs/services",
        access: "Customer",
        icon: FileSearch2,
      },
      {
        label: "Track & Trace",
        href: "/customer/cfs/track-trace",
        access: "Customer",
        icon: MapPinned,
      },
      {
        label: "Tariff Upload",
        href: "/customer/cfs/tariff-upload",
        access: "Customer",
        icon: UploadIcon,
      },
      {
        label: "EIR Copy",
        href: "/customer/cfs/services/eir-cop",
        access: "Customer",
        icon: FileText,
      },
      {
        label: "Proforma Invoice",
        href: "/customer/cfs/services/proforma-invoice",
        access: "Customer",
        icon: Files,
      },
      {
        label: "Priority Movements",
        href: "/customer/cfs/services/priority",
        access: "Customer",
        icon: FastForward,
      },
      {
        label: "Weighment Slip",
        href: "/customer/cfs/services/weighment-slip",
        access: "Customer",
        icon: Scale,
      },
      {
        label: "Special Equipment",
        href: "/customer/cfs/services/special-equipment",
        access: "Customer",
        icon: LayoutGrid,
      },
      {
        label: "Container Grounding",
        href: "/customer/cfs/services/container-grounding",
        access: "Customer",
        icon: ArrowDown,
      },
      {
        label: "Container Staging",
        href: "/customer/cfs/services/container-staging",
        access: "Customer",
        icon: Boxes,
      },
      {
        label: "Re-Scanning",
        href: "/customer/cfs/services/rescan",
        access: "Customer",
        icon: Scan,
      },
      {
        label: "Cheque Acceptance",
        href: "/customer/cfs/services/cheque",
        access: "Customer",
        icon: CreditCard,
      },
      {
        label: "Tax Invoice",
        href: "/customer/cfs/services/tax-invoice",
        access: "Customer",
        icon: Receipt,
      },
      {
        label: "Job Order Update",
        href: "/customer/cfs/services/job-order",
        access: "Customer",
        icon: FileText,
      },
    ],
  },
  {
    label: "Warehouse",
    href: "",
    icon: Warehouse,
    access: "Customer",
    subItems: [
      {
        label: "Orders",
        href: "/customer/warehouse/orders",
        access: "Customer",
        icon: Package,
      },
      {
        label: "Pricing Requests",
        href: "/customer/warehouse/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "Customer",
      },
      {
        label: "Service Requests",
        href: "/customer/warehouse/requests",
        icon: MailQuestion,
        access: "Customer",
      },
      {
        label: "Services",
        href: "/customer/warehouse/services",
        access: "Customer",
        icon: FileSearch2,
      },
      {
        label: "Track & Trace",
        href: "/customer/warehouse/track-trace",
        access: "Customer",
        icon: MapPinned,
      },
      {
        label: "Tariff Upload",
        href: "/customer/warehouse/tariff-upload",
        access: "Customer",
        icon: UploadIcon,
      },
      {
        label: "EIR Copy",
        href: "/customer/warehouse/services/eir-cop",
        access: "Customer",
        icon: FileText,
      },
      {
        label: "Priority Movements",
        href: "/customer/warehouse/services/priority",
        access: "Customer",
        icon: FastForward,
      },
      {
        label: "Weighment Slip",
        href: "/customer/warehouse/services/weighment-slip",
        access: "Customer",
        icon: Scale,
      },
      {
        label: "Special Equipment",
        href: "/customer/warehouse/services/special-equipment",
        access: "Customer",
        icon: LayoutGrid,
      },
      {
        label: "Container Grounding",
        href: "/customer/warehouse/services/container-grounding",
        access: "Customer",
        icon: ArrowDown,
      },
      {
        label: "Container Staging",
        href: "/customer/warehouse/services/container-staging",
        access: "Customer",
        icon: Boxes,
      },
      {
        label: "Re-Scanning",
        href: "/customer/warehouse/services/rescan",
        access: "Customer",
        icon: Scan,
      },
      {
        label: "Cheque Acceptance",
        href: "/customer/warehouse/services/cheque",
        access: "Customer",
        icon: CreditCard,
      },
      {
        label: "Tax Invoice",
        href: "/customer/warehouse/services/tax-invoice",
        access: "Customer",
        icon: Receipt,
      },
      {
        label: "Job Order Update",
        href: "/customer/warehouse/services/job-order",
        access: "Customer",
        icon: FileText,
      },
    ],
  },
  {
    label: "Transport",
    href: "",
    icon: Truck,
    access: "Customer",
    subItems: [
      {
        label: "Orders",
        href: "/customer/transport/orders",
        icon: Package,
        access: "Customer",
      },
      {
        label: "Transport Movement",
        href: "/customer/transport/services/order-movements",
        access: "Customer",
        icon: MapPinned,
      },
      {
        label: "Pricing Requests",
        href: "/customer/transport/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "Customer",
      },
      {
        label: "Service Requests",
        href: "/customer/transport/requests",
        icon: MailQuestion,
        access: "Customer",
      },
      {
        label: "Job Order Update",
        href: "/customer/transport/services/job-order",
        access: "Customer",
        icon: FileText,
      },
    ],
  },
  {
    label: "3PL",
    href: "",
    icon: Handshake,
    access: "Customer",
    subItems: [
      {
        label: "Orders",
        href: "/customer/3pl/orders",
        icon: Package,
        access: "Customer",
      },
      {
        label: "Pricing Requests",
        href: "/customer/3pl/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "Customer",
      },
      {
        label: "CFS",
        href: "",
        icon: Ship,
        access: "Customer",
        subItems: [
          {
            label: "Services",
            href: "/customer/3pl/services/cfs",
            access: "Customer",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/customer/3pl/services/cfs/track-trace",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/3pl/services/cfs/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Tariff Upload",
            href: "/customer/3pl/services/cfs/tariff-upload",
            access: "Customer",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/customer/3pl/services/cfs/eir-cop",
            access: "Customer",
            icon: FileText,
          },
          {
            label: "Proforma Invoice",
            href: "/customer/3pl/services/cfs/proforma-invoice",
            access: "Customer",
            icon: Files,
          },
          {
            label: "Priority Movements",
            href: "/customer/3pl/services/cfs/priority",
            access: "Customer",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/customer/3pl/services/cfs/weighment-slip",
            access: "Customer",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/customer/3pl/services/cfs/special-equipment",
            access: "Customer",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/customer/3pl/services/cfs/container-grounding",
            access: "Customer",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/customer/3pl/services/cfs/container-staging",
            access: "Customer",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/customer/3pl/services/cfs/rescan",
            access: "Customer",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/customer/3pl/services/cfs/cheque",
            access: "Customer",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/customer/3pl/services/cfs/tax-invoice",
            access: "Customer",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/customer/3pl/services/cfs/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
      {
        label: "Warehouse",
        href: "",
        icon: Warehouse,
        access: "Customer",
        subItems: [
          {
            label: "Services",
            href: "/customer/3pl/services/warehouse",
            access: "Customer",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/customer/3pl/services/warehouse/track-trace",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/3pl/services/warehouse/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Tariff Upload",
            href: "/customer/3pl/services/warehouse/tariff-upload",
            access: "Customer",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/customer/3pl/services/warehouse/eir-cop",
            access: "Customer",
            icon: FileText,
          },
          {
            label: "Priority Movements",
            href: "/customer/3pl/services/warehouse/priority",
            access: "Customer",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/customer/3pl/services/warehouse/weighment-slip",
            access: "Customer",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/customer/3pl/services/warehouse/special-equipment",
            access: "Customer",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/customer/3pl/services/warehouse/container-grounding",
            access: "Customer",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/customer/3pl/services/warehouse/container-staging",
            access: "Customer",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/customer/3pl/services/warehouse/rescan",
            access: "Customer",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/customer/3pl/services/warehouse/cheque",
            access: "Customer",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/customer/3pl/services/warehouse/tax-invoice",
            access: "Customer",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/customer/3pl/services/warehouse/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
      {
        label: "Transport",
        href: "",
        icon: Truck,
        access: "Customer",
        subItems: [
          {
            label: "Transport Movement",
            href: "/customer/3pl/services/transport/order-movements",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/3pl/services/transport/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Job Order Update",
            href: "/customer/3pl/services/transport/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
    ],
  },
  {
    label: "Custom",
    href: "",
    icon: PackagePlus,
    access: "Customer",
    subItems: [
      {
        label: "Orders Packages",
        href: "/customer/custom/order-packages",
        icon: Blocks,
        access: "Customer",
      },
      {
        label: "Pricing Requests",
        href: "/customer/custom/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "Customer",
      },
      {
        label: "CFS",
        href: "",
        icon: Ship,
        access: "Customer",
        subItems: [
          {
            label: "Orders",
            href: "/customer/custom/services/cfs/orders",
            icon: Package,
            access: "Customer",
          },
          {
            label: "Services",
            href: "/customer/custom/services/cfs",
            access: "Customer",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/customer/custom/services/cfs/track-trace",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/custom/services/cfs/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Tariff Upload",
            href: "/customer/custom/services/cfs/tariff-upload",
            access: "Customer",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/customer/custom/services/cfs/eir-cop",
            access: "Customer",
            icon: FileText,
          },
          {
            label: "Proforma Invoice",
            href: "/customer/custom/services/cfs/proforma-invoice",
            access: "Customer",
            icon: Files,
          },
          {
            label: "Priority Movements",
            href: "/customer/custom/services/cfs/priority",
            access: "Customer",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/customer/custom/services/cfs/weighment-slip",
            access: "Customer",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/customer/custom/services/cfs/special-equipment",
            access: "Customer",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/customer/custom/services/cfs/container-grounding",
            access: "Customer",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/customer/custom/services/cfs/container-staging",
            access: "Customer",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/customer/custom/services/cfs/rescan",
            access: "Customer",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/customer/custom/services/cfs/cheque",
            access: "Customer",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/customer/custom/services/cfs/tax-invoice",
            access: "Customer",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/customer/custom/services/cfs/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
      {
        label: "Warehouse",
        href: "",
        icon: Warehouse,
        access: "Customer",
        subItems: [
          {
            label: "Orders",
            href: "/customer/custom/services/warehouse/orders",
            icon: Package,
            access: "Customer",
          },
          {
            label: "Services",
            href: "/customer/custom/services/warehouse",
            access: "Customer",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/customer/custom/services/warehouse/track-trace",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/custom/services/warehouse/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Tariff Upload",
            href: "/customer/custom/services/warehouse/tariff-upload",
            access: "Customer",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/customer/custom/services/warehouse/eir-cop",
            access: "Customer",
            icon: FileText,
          },
          {
            label: "Priority Movements",
            href: "/customer/custom/services/warehouse/priority",
            access: "Customer",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/customer/custom/services/warehouse/weighment-slip",
            access: "Customer",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/customer/custom/services/warehouse/special-equipment",
            access: "Customer",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/customer/custom/services/warehouse/container-grounding",
            access: "Customer",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/customer/custom/services/warehouse/container-staging",
            access: "Customer",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/customer/custom/services/warehouse/rescan",
            access: "Customer",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/customer/custom/services/warehouse/cheque",
            access: "Customer",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/customer/custom/services/warehouse/tax-invoice",
            access: "Customer",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/customer/custom/services/warehouse/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
      {
        label: "Transport",
        href: "",
        icon: Truck,
        access: "Customer",
        subItems: [
          {
            label: "Orders",
            href: "/customer/custom/services/transport/orders",
            icon: Package,
            access: "Customer",
          },
          {
            label: "Transport Movement",
            href: "/customer/custom/services/transport/order-movements",
            access: "Customer",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/customer/custom/services/transport/requests",
            icon: MailQuestion,
            access: "Customer",
          },
          {
            label: "Job Order Update",
            href: "/customer/custom/services/transport/job-order",
            access: "Customer",
            icon: FileText,
          },
        ],
      },
    ],
  },
  {
    label: "Containers Management",
    href: "/customer/container-management",
    access: "Customer",
    icon: Container,
  },
  {
    label: "Notifications & Updates",
    href: "",
    access: "Customer",
    icon: Bell,
  },
  {
    label: "Profile",
    href: "/customer/profile",
    access: "Customer",
    icon: User,
  },
  {
    label: "Support",
    href: "/customer/support",
    access: "Customer",
    icon: Info,
  },

  // Merchant
  {
    label: "Dashboard",
    href: "/client/dashboard",
    icon: LayoutDashboard,
    access: "Client",
  },
  {
    label: "CFS",
    href: "",
    icon: Ship,
    access: "Client",
    subItems: [
      {
        label: "Orders",
        href: "/client/cfs/orders",
        access: "Client",
        icon: Package,
      },
      {
        label: "Order Movement",
        href: "/client/cfs/order-movement",
        access: "Client",
        icon: MapPinned,
      },
      {
        label: "Service Requests",
        href: "/client/cfs/requests",
        icon: MailQuestion,
        access: "Client",
      },
      {
        label: "Services",
        href: "/client/cfs/services",
        access: "Client",
        icon: FileSearch2,
      },
      {
        label: "Tariff Upload",
        href: "/client/cfs/tariff-upload",
        access: "Client",
        icon: UploadIcon,
      },
      {
        label: "EIR Copy",
        href: "/client/cfs/services/eir-cop",
        access: "Client",
        icon: FileText,
      },
      {
        label: "Proforma Invoice",
        href: "/client/cfs/services/proforma-invoice",
        access: "Customer",
        icon: Files,
      },
      {
        label: "Priority Movements",
        href: "/client/cfs/services/priority",
        access: "Client",
        icon: FastForward,
      },
      {
        label: "Weighment Slip",
        href: "/client/cfs/services/weighment-slip",
        access: "Client",
        icon: Scale,
      },
      {
        label: "Special Equipment",
        href: "/client/cfs/services/special-equipment",
        access: "Client",
        icon: LayoutGrid,
      },
      {
        label: "Container Grounding",
        href: "/client/cfs/services/container-grounding",
        access: "Client",
        icon: ArrowDown,
      },
      {
        label: "Container Staging",
        href: "/client/cfs/services/container-staging",
        access: "Client",
        icon: Boxes,
      },
      {
        label: "Re-Scanning",
        href: "/client/cfs/services/rescan",
        access: "Client",
        icon: Scan,
      },
      {
        label: "Cheque Acceptance",
        href: "/client/cfs/services/cheque",
        access: "Client",
        icon: CreditCard,
      },
      {
        label: "Tax Invoice",
        href: "/client/cfs/services/tax-invoice",
        access: "Client",
        icon: Receipt,
      },
      {
        label: "Job Orders",
        href: "/client/cfs/services/job-order",
        access: "Client",
        icon: FileText,
      },
    ],
  },
  {
    label: "Warehouse",
    href: "",
    icon: Warehouse,
    access: "Client",
    subItems: [
      {
        label: "Orders",
        href: "/client/warehouse/orders",
        access: "Client",
        icon: Package,
      },
      {
        label: "Order Movement",
        href: "/client/warehouse/order-movement",
        access: "Client",
        icon: MapPinned,
      },
      {
        label: "Service Requests",
        href: "/client/warehouse/requests",
        icon: MailQuestion,
        access: "Client",
      },
      {
        label: "Services",
        href: "/client/warehouse/services",
        access: "Client",
        icon: FileSearch2,
      },
      {
        label: "Tariff Upload",
        href: "/client/warehouse/tariff-upload",
        access: "Client",
        icon: UploadIcon,
      },
      {
        label: "EIR Copy",
        href: "/client/warehouse/services/eir-cop",
        access: "Client",
        icon: FileText,
      },
      {
        label: "Priority Movements",
        href: "/client/warehouse/services/priority",
        access: "Client",
        icon: FastForward,
      },
      {
        label: "Weighment Slip",
        href: "/client/warehouse/services/weighment-slip",
        access: "Client",
        icon: Scale,
      },
      {
        label: "Special Equipment",
        href: "/client/warehouse/services/special-equipment",
        access: "Client",
        icon: LayoutGrid,
      },
      {
        label: "Container Grounding",
        href: "/client/warehouse/services/container-grounding",
        access: "Client",
        icon: ArrowDown,
      },
      {
        label: "Container Staging",
        href: "/client/warehouse/services/container-staging",
        access: "Client",
        icon: Boxes,
      },
      {
        label: "Re-Scanning",
        href: "/client/warehouse/services/rescan",
        access: "Client",
        icon: Scan,
      },
      {
        label: "Cheque Acceptance",
        href: "/client/warehouse/services/cheque",
        access: "Client",
        icon: CreditCard,
      },
      {
        label: "Tax Invoice",
        href: "/client/warehouse/services/tax-invoice",
        access: "Client",
        icon: Receipt,
      },
      {
        label: "Job Orders",
        href: "/client/warehouse/services/job-order",
        access: "Client",
        icon: FileText,
      },
    ],
  },
  {
    label: "Transport",
    href: "",
    icon: Truck,
    access: "Client",
    subItems: [
      {
        label: "Orders",
        href: "/client/transport/orders",
        icon: Package,
        access: "Client",
      },
      {
        label: "Transport Movement",
        href: "/client/transport/services/order-movements",
        access: "Client",
        icon: MapPinned,
      },
      {
        label: "Service Requests",
        href: "/client/transport/requests",
        icon: MailQuestion,
        access: "Client",
      },
      {
        label: "Job Orders",
        href: "/client/transport/services/job-order",
        access: "Client",
        icon: FileText,
      },
    ],
  },
  {
    label: "3PL",
    href: "",
    icon: Handshake,
    access: "Client",
    subItems: [
      {
        label: "Orders",
        href: "/client/3pl/orders",
        icon: Package,
        access: "Client",
      },
      {
        label: "CFS",
        href: "",
        icon: Ship,
        access: "Client",
        subItems: [
          {
            label: "Order Movement",
            href: "/client/3pl/services/cfs/order-movement",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Services",
            href: "/client/3pl/services/cfs",
            access: "Client",
            icon: FileSearch2,
          },
          {
            label: "Service Requests",
            href: "/client/3pl/services/cfs/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Track & Trace",
            href: "/client/3pl/services/cfs/track-trace",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Tariff Upload",
            href: "/client/3pl/services/cfs/tariff-upload",
            access: "Client",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/client/3pl/services/cfs/eir-cop",
            access: "Client",
            icon: FileText,
          },
          {
            label: "Proforma Invoice",
            href: "/client/3pl/services/cfs/proforma-invoice",
            access: "Customer",
            icon: Files,
          },
          {
            label: "Priority Movements",
            href: "/client/3pl/services/cfs/priority",
            access: "Client",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/client/3pl/services/cfs/weighment-slip",
            access: "Client",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/client/3pl/services/cfs/special-equipment",
            access: "Client",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/client/3pl/services/cfs/container-grounding",
            access: "Client",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/client/3pl/services/cfs/container-staging",
            access: "Client",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/client/3pl/services/cfs/rescan",
            access: "Client",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/client/3pl/services/cfs/cheque",
            access: "Client",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/client/3pl/services/cfs/tax-invoice",
            access: "Client",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/client/3pl/services/cfs/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
      {
        label: "Warehouse",
        href: "",
        icon: Warehouse,
        access: "Client",
        subItems: [
          {
            label: "Order Movement",
            href: "/client/3pl/services/warehouse/order-movement",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Services",
            href: "/client/3pl/services/warehouse",
            access: "Client",
            icon: FileSearch2,
          },
          {
            label: "Service Requests",
            href: "/client/3pl/services/warehouse/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Track & Trace",
            href: "/client/3pl/services/warehouse/track-trace",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Tariff Upload",
            href: "/client/3pl/services/warehouse/tariff-upload",
            access: "Client",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/client/3pl/services/warehouse/eir-cop",
            access: "Client",
            icon: FileText,
          },
          {
            label: "Priority Movements",
            href: "/client/3pl/services/warehouse/priority",
            access: "Client",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/client/3pl/services/warehouse/weighment-slip",
            access: "Client",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/client/3pl/services/warehouse/special-equipment",
            access: "Client",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/client/3pl/services/warehouse/container-grounding",
            access: "Client",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/client/3pl/services/warehouse/container-staging",
            access: "Client",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/client/3pl/services/warehouse/rescan",
            access: "Client",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/client/3pl/services/warehouse/cheque",
            access: "Client",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/client/3pl/services/warehouse/tax-invoice",
            access: "Client",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/client/3pl/services/warehouse/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
      {
        label: "Transport",
        href: "",
        icon: Truck,
        access: "Client",
        subItems: [
          {
            label: "Transport Movement",
            href: "/client/3pl/services/transport/order-movements",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/client/3pl/services/transport/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Job Order Update",
            href: "/client/3pl/services/transport/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
    ],
  },
  {
    label: "Custom",
    href: "",
    icon: PackagePlus,
    access: "Client",
    subItems: [
      {
        label: "Orders Packages",
        href: "/client/custom/order-packages",
        icon: Blocks,
        access: "Client",
      },
      {
        label: "CFS",
        href: "",
        icon: Ship,
        access: "",
        subItems: [
          {
            label: "Orders",
            href: "/client/custom/services/cfs/orders",
            icon: Package,
            access: "Client",
          },
          {
            label: "Services",
            href: "/client/custom/services/cfs",
            access: "Client",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/client/custom/services/cfs/track-trace",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/client/custom/services/cfs/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Tariff Upload",
            href: "/client/custom/services/cfs/tariff-upload",
            access: "Client",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/client/custom/services/cfs/eir-cop",
            access: "Client",
            icon: FileText,
          },
          {
            label: "Proforma Invoice",
            href: "/client/custom/services/cfs/proforma-invoice",
            access: "Client",
            icon: Files,
          },
          {
            label: "Priority Movements",
            href: "/client/custom/services/cfs/priority",
            access: "Client",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/client/custom/services/cfs/weighment-slip",
            access: "Client",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/client/custom/services/cfs/special-equipment",
            access: "Client",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/client/custom/services/cfs/container-grounding",
            access: "Client",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/client/custom/services/cfs/container-staging",
            access: "Client",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/client/custom/services/cfs/rescan",
            access: "Client",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/client/custom/services/cfs/cheque",
            access: "Client",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/client/custom/services/cfs/tax-invoice",
            access: "Client",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/client/custom/services/cfs/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
      {
        label: "Warehouse",
        href: "",
        icon: Warehouse,
        access: "Client",
        subItems: [
          {
            label: "Orders",
            href: "/client/custom/services/warehouse/orders",
            icon: Package,
            access: "Client",
          },
          {
            label: "Services",
            href: "/client/custom/services/warehouse",
            access: "Client",
            icon: FileSearch2,
          },
          {
            label: "Track & Trace",
            href: "/client/custom/services/warehouse/track-trace",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/client/custom/services/warehouse/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Tariff Upload",
            href: "/client/custom/services/warehouse/tariff-upload",
            access: "Client",
            icon: UploadIcon,
          },
          {
            label: "EIR Copy",
            href: "/client/custom/services/warehouse/eir-cop",
            access: "Client",
            icon: FileText,
          },
          {
            label: "Priority Movements",
            href: "/client/custom/services/warehouse/priority",
            access: "Client",
            icon: FastForward,
          },
          {
            label: "Weighment Slip",
            href: "/client/custom/services/warehouse/weighment-slip",
            access: "Client",
            icon: Scale,
          },
          {
            label: "Special Equipment",
            href: "/client/custom/services/warehouse/special-equipment",
            access: "Client",
            icon: LayoutGrid,
          },
          {
            label: "Container Grounding",
            href: "/client/custom/services/warehouse/container-grounding",
            access: "Client",
            icon: ArrowDown,
          },
          {
            label: "Container Staging",
            href: "/client/custom/services/warehouse/container-staging",
            access: "Client",
            icon: Boxes,
          },
          {
            label: "Re-Scanning",
            href: "/client/custom/services/warehouse/rescan",
            access: "Client",
            icon: Scan,
          },
          {
            label: "Cheque Acceptance",
            href: "/client/custom/services/warehouse/cheque",
            access: "Client",
            icon: CreditCard,
          },
          {
            label: "Tax Invoice",
            href: "/client/custom/services/warehouse/tax-invoice",
            access: "Client",
            icon: Receipt,
          },
          {
            label: "Job Order Update",
            href: "/client/custom/services/warehouse/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
      {
        label: "Transport",
        href: "",
        icon: Truck,
        access: "Client",
        subItems: [
          {
            label: "Orders",
            href: "/client/custom/services/transport/orders",
            icon: Package,
            access: "Client",
          },
          {
            label: "Transport Movement",
            href: "/client/custom/services/transport/order-movements",
            access: "Client",
            icon: MapPinned,
          },
          {
            label: "Service Requests",
            href: "/client/custom/services/transport/requests",
            icon: MailQuestion,
            access: "Client",
          },
          {
            label: "Job Order Update",
            href: "/client/custom/services/transport/job-order",
            access: "Client",
            icon: FileText,
          },
        ],
      },
    ],
  },
  {
    label: "Vehicles Management",
    href: "/client/vehicle-management",
    access: "Client",
    icon: TruckElectric,
  },
  { label: "Notifications & Updates", href: "", access: "Client", icon: Bell },
  {
    label: "Profile",
    href: "/client/profile",
    access: "Client",
    icon: User,
  },
  {
    label: "Customer Chat",
    href: "/client/customer-chat",
    access: "Client",
    icon: Info,
  },

  // GOL
  {
    label: "Dashboard",
    href: "/gol/dashboard",
    icon: LayoutDashboard,
    access: "GOL",
  },
  {
    label: "CFS",
    href: "",
    icon: Ship,
    access: "GOL",
    subItems: [
      {
        label: "Orders",
        href: "/gol/cfs/orders",
        icon: Package,
        access: "GOL",
      },
      {
        label: "Service Requests",
        href: "/gol/cfs/requests",
        icon: MailQuestion,
        access: "GOL",
      },
      {
        label: "Pricing Requests",
        href: "/gol/cfs/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "GOL",
      },
      {
        label: "Tariff Upload",
        href: "/gol/cfs/tariff-upload",
        access: "GOL",
        icon: UploadIcon,
      },
    ],
  },
  {
    label: "Warehouse",
    href: "",
    icon: Warehouse,
    access: "GOL",
    subItems: [
      {
        label: "Orders",
        href: "/gol/warehouse/orders",
        icon: Package,
        access: "GOL",
      },
      {
        label: "Service Requests",
        href: "/gol/warehouse/requests",
        icon: MailQuestion,
        access: "GOL",
      },
      {
        label: "Pricing Requests",
        href: "/gol/warehouse/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "GOL",
      },
      {
        label: "Tariff Upload",
        href: "/gol/warehouse/tariff-upload",
        access: "GOL",
        icon: UploadIcon,
      },
    ],
  },
  {
    label: "Transport",
    href: "",
    icon: Truck,
    access: "GOL",
    subItems: [
      {
        label: "Orders",
        href: "/gol/transport/orders",
        icon: Package,
        access: "GOL",
      },
      {
        label: "Pricing Requests",
        href: "/gol/transport/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "GOL",
      },
      {
        label: "Service Requests",
        href: "/gol/transport/requests",
        icon: MailQuestion,
        access: "GOL",
      },
    ],
  },
  {
    label: "3PL",
    href: "",
    icon: Handshake,
    access: "GOL",
    subItems: [
      {
        label: "Orders",
        href: "/gol/3pl/orders",
        icon: Package,
        access: "GOL",
      },
      {
        label: "Pricing Requests",
        href: "/gol/3pl/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "GOL",
      },
      {
        label: "Service Requests",
        href: "/gol/3pl/requests",
        icon: MailQuestion,
        access: "GOL",
      },
    ],
  },
  {
    label: "Custom",
    href: "",
    icon: PackagePlus,
    access: "GOL",
    subItems: [
      {
        label: "Orders Packages",
        href: "/gol/custom/order-packages",
        icon: Blocks,
        access: "Client",
      },
      {
        label: "Pricing Requests",
        href: "/gol/custom/pricing-requests",
        icon: ReceiptIndianRupee,
        access: "GOL",
      },
      {
        label: "CFS",
        href: "",
        icon: Ship,
        access: "GOL",
        subItems: [
          {
            label: "Orders",
            href: "/gol/custom/services/cfs/orders",
            icon: Package,
            access: "Client",
          },
        ]
      },
      {
        label: "Warehouse",
        href: "",
        icon: Warehouse,
        access: "GOL",
        subItems: [
          {
            label: "Orders",
            href: "/gol/custom/services/warehouse/orders",
            icon: Package,
            access: "Client",
          },
        ]
      },
      {
        label: "Transport",
        href: "",
        icon: Truck,
        access: "GOL",
        subItems: [
          {
            label: "Orders",
            href: "/gol/custom/services/transport/orders",
            icon: Package,
            access: "Client",
          },
        ]
      },
    ],
  },
  {
    label: "Packages Management",
    href: "/gol/package-management",
    access: "GOL",
    icon: Package2,
  },
  {
		label: "User Management",
		href: "/gol/user_management",
		icon: Users,
		access: 'GOL',
	},
  {
		label: "User Verification",
		href: "/gol/user_verification",
		icon: User,
		access: 'GOL',
	},
  {
    label: "Audit Logs",
    href: "/gol/audit-log",
    access: "GOL",
    icon: FileLock2,
  },
  {
    label: "Backup & Restore",
    href: "/gol/backup-restore",
    access: "GOL",
    icon: DatabaseBackup,
  },
];